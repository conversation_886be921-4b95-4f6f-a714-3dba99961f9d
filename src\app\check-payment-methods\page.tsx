'use client';

/**
 * Real-time PayMongo Payment Methods Checker
 * Shows exactly what payment methods are available in your account
 */

import React, { useState, useEffect } from 'react';

interface PaymentMethodStatus {
  method: string;
  available: boolean;
  status: string;
  processingTime: string;
  description: string;
}

export default function CheckPaymentMethodsPage() {
  const [loading, setLoading] = useState(true);
  const [apiResponse, setApiResponse] = useState<any>(null);
  const [error, setError] = useState<string>('');

  const checkAvailableMethods = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/paymongo/available-methods');
      const result = await response.json();

      if (response.ok) {
        setApiResponse(result);
      } else {
        setError(result.error || 'Failed to check payment methods');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Network error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    checkAvailableMethods();
  }, []);

  // Expected payment methods based on PayMongo documentation
  const expectedMethods: PaymentMethodStatus[] = [
    {
      method: 'card',
      available: true,
      status: 'Should be available immediately',
      processingTime: '5 business days for full activation',
      description: 'Visa/Mastercard debit and credit cards'
    },
    {
      method: 'gcash',
      available: true,
      status: 'Activated upon account activation',
      processingTime: '5 business days',
      description: 'GCash e-wallet payments'
    },
    {
      method: 'grab_pay',
      available: true,
      status: 'Activated upon account activation',
      processingTime: '5 business days',
      description: 'GrabPay e-wallet payments'
    },
    {
      method: 'paymaya',
      available: true,
      status: 'Activated upon account activation',
      processingTime: '5 business days',
      description: 'Maya (formerly PayMaya) e-wallet payments'
    },
    {
      method: 'dob',
      available: true,
      status: 'BPI/UBP activated upon account activation',
      processingTime: '3 business days',
      description: 'Direct Online Banking (BPI, UBP, BDO, Metrobank, LandBank)'
    },
    {
      method: 'billease',
      available: false,
      status: 'Requires manual request',
      processingTime: '3 business days after request',
      description: 'Buy Now, Pay Later through BillEase'
    },
    {
      method: 'qrph',
      available: false,
      status: 'Requires manual request',
      processingTime: '3 business days after request',
      description: 'QR Ph scan-to-pay'
    }
  ];

  const getMethodStatus = (method: string) => {
    if (!apiResponse?.data?.payment_intent?.payment_method_allowed) {
      return 'Unknown';
    }
    
    const allowedMethods = apiResponse.data.payment_intent.payment_method_allowed;
    return allowedMethods.includes(method) ? 'Available' : 'Not Available';
  };

  const getStatusColor = (method: string) => {
    const status = getMethodStatus(method);
    if (status === 'Available') return 'text-green-600 bg-green-50';
    if (status === 'Not Available') return 'text-red-600 bg-red-50';
    return 'text-gray-600 bg-gray-50';
  };

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f9fafb', padding: '1rem' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto', fontFamily: 'system-ui, sans-serif' }}>
        
        {/* Header */}
        <div style={{ 
          backgroundColor: 'white', 
          borderRadius: '8px', 
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)', 
          padding: '2rem', 
          textAlign: 'center',
          marginBottom: '1.5rem'
        }}>
          <h1 style={{ 
            fontSize: '2rem', 
            fontWeight: 'bold', 
            color: '#f3a823', 
            marginBottom: '0.5rem' 
          }}>
            PayMongo Payment Methods Status
          </h1>
          <p style={{ color: '#6b7280' }}>
            Real-time check of available payment methods in your business account
          </p>
        </div>

        {/* API Response Status */}
        <div style={{ 
          backgroundColor: 'white', 
          borderRadius: '8px', 
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)', 
          padding: '1.5rem',
          marginBottom: '1.5rem'
        }}>
          <h2 style={{ fontSize: '1.25rem', fontWeight: '600', marginBottom: '1rem' }}>
            API Connection Status
          </h2>
          
          {loading && (
            <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
              <div style={{ 
                width: '20px', 
                height: '20px', 
                border: '2px solid #f3a823', 
                borderTop: '2px solid transparent', 
                borderRadius: '50%', 
                animation: 'spin 1s linear infinite' 
              }}></div>
              <span>Checking PayMongo API...</span>
            </div>
          )}

          {error && (
            <div style={{ 
              backgroundColor: '#fef2f2', 
              border: '1px solid #fecaca', 
              borderRadius: '8px', 
              padding: '1rem',
              color: '#dc2626'
            }}>
              <strong>Error:</strong> {error}
            </div>
          )}

          {apiResponse && (
            <div style={{ 
              backgroundColor: '#f0fdf4', 
              border: '1px solid #bbf7d0', 
              borderRadius: '8px', 
              padding: '1rem',
              color: '#166534'
            }}>
              <div style={{ marginBottom: '0.5rem' }}>
                <strong>✅ Connected to PayMongo API</strong>
              </div>
              <div style={{ fontSize: '0.875rem' }}>
                Environment: {apiResponse.data.environment} | 
                Payment Intent: {apiResponse.data.payment_intent.id}
              </div>
            </div>
          )}
        </div>

        {/* Payment Methods Status */}
        <div style={{ 
          backgroundColor: 'white', 
          borderRadius: '8px', 
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)', 
          padding: '1.5rem',
          marginBottom: '1.5rem'
        }}>
          <h2 style={{ fontSize: '1.25rem', fontWeight: '600', marginBottom: '1rem' }}>
            Payment Methods Status
          </h2>

          <div style={{ overflowX: 'auto' }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr style={{ backgroundColor: '#f9fafb' }}>
                  <th style={{ padding: '0.75rem', textAlign: 'left', borderBottom: '1px solid #e5e7eb' }}>
                    Payment Method
                  </th>
                  <th style={{ padding: '0.75rem', textAlign: 'left', borderBottom: '1px solid #e5e7eb' }}>
                    Current Status
                  </th>
                  <th style={{ padding: '0.75rem', textAlign: 'left', borderBottom: '1px solid #e5e7eb' }}>
                    Expected Status
                  </th>
                  <th style={{ padding: '0.75rem', textAlign: 'left', borderBottom: '1px solid #e5e7eb' }}>
                    Processing Time
                  </th>
                </tr>
              </thead>
              <tbody>
                {expectedMethods.map((method, index) => (
                  <tr key={method.method} style={{ borderBottom: '1px solid #f3f4f6' }}>
                    <td style={{ padding: '0.75rem' }}>
                      <div>
                        <div style={{ fontWeight: '500' }}>
                          {method.method.replace('_', ' ').toUpperCase()}
                        </div>
                        <div style={{ fontSize: '0.875rem', color: '#6b7280' }}>
                          {method.description}
                        </div>
                      </div>
                    </td>
                    <td style={{ padding: '0.75rem' }}>
                      <span style={{ 
                        padding: '0.25rem 0.5rem', 
                        borderRadius: '4px', 
                        fontSize: '0.875rem',
                        fontWeight: '500'
                      }} className={getStatusColor(method.method)}>
                        {getMethodStatus(method.method)}
                      </span>
                    </td>
                    <td style={{ padding: '0.75rem', fontSize: '0.875rem' }}>
                      {method.status}
                    </td>
                    <td style={{ padding: '0.75rem', fontSize: '0.875rem' }}>
                      {method.processingTime}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Currently Available Methods */}
        {apiResponse?.data?.payment_intent?.payment_method_allowed && (
          <div style={{ 
            backgroundColor: 'white', 
            borderRadius: '8px', 
            boxShadow: '0 1px 3px rgba(0,0,0,0.1)', 
            padding: '1.5rem',
            marginBottom: '1.5rem'
          }}>
            <h2 style={{ fontSize: '1.25rem', fontWeight: '600', marginBottom: '1rem' }}>
              Currently Available in Your Account
            </h2>
            
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '1rem' }}>
              {apiResponse.data.payment_intent.payment_method_allowed.map((method: string) => (
                <div key={method} style={{ 
                  backgroundColor: '#f0fdf4', 
                  border: '1px solid #bbf7d0', 
                  borderRadius: '8px', 
                  padding: '1rem',
                  textAlign: 'center'
                }}>
                  <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>
                    {method === 'card' && '💳'}
                    {method === 'gcash' && '📱'}
                    {method === 'grab_pay' && '🚗'}
                    {method === 'paymaya' && '💰'}
                    {method === 'dob' && '🏛️'}
                    {method === 'billease' && '🏦'}
                    {method === 'qrph' && '📱'}
                  </div>
                  <div style={{ fontWeight: '500', color: '#166534' }}>
                    {method.replace('_', ' ').toUpperCase()}
                  </div>
                  <div style={{ fontSize: '0.75rem', color: '#16a34a' }}>
                    ✅ Available
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Refresh Button */}
        <div style={{ textAlign: 'center' }}>
          <button
            onClick={checkAvailableMethods}
            disabled={loading}
            style={{
              backgroundColor: loading ? '#d1d5db' : '#f3a823',
              color: 'white',
              padding: '0.75rem 1.5rem',
              borderRadius: '8px',
              border: 'none',
              fontWeight: '500',
              cursor: loading ? 'not-allowed' : 'pointer',
              fontSize: '1rem'
            }}
          >
            {loading ? 'Checking...' : 'Refresh Status'}
          </button>
        </div>

        {/* Information */}
        <div style={{ 
          backgroundColor: '#dbeafe', 
          border: '1px solid #93c5fd', 
          borderRadius: '8px', 
          padding: '1rem',
          marginTop: '1.5rem'
        }}>
          <div style={{ fontSize: '0.875rem', color: '#1e40af' }}>
            <strong>Note:</strong> Payment methods may take up to 5 business days to be fully activated after account verification. 
            If you don't see GCash or other e-wallets, they may still be processing. Contact PayMongo support if methods are missing after the processing period.
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}

'use client';

/**
 * Payment Callback Page
 * Handles redirects from PayMongo after 3D Secure authentication or e-wallet payments
 */

import React, { useEffect, useState } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, XCircle, ArrowLeft } from 'lucide-react';

export default function PaymentCallbackPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  
  const [status, setStatus] = useState<'loading' | 'success' | 'failed' | 'error'>('loading');
  const [paymentData, setPaymentData] = useState<any>(null);
  const [error, setError] = useState<string>('');
  
  const orderId = searchParams.get('order_id');
  const paymentIntentId = searchParams.get('payment_intent_id');
  const clientKey = searchParams.get('client_key');

  useEffect(() => {
    if (paymentIntentId && clientKey) {
      checkPaymentStatus();
    } else {
      setStatus('error');
      setError('Missing payment information in callback URL');
    }
  }, [paymentIntentId, clientKey]);

  const checkPaymentStatus = async () => {
    try {
      const response = await fetch(
        `/api/payments/status/${paymentIntentId}?client_key=${clientKey}`
      );
      
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to check payment status');
      }

      const paymentStatus = result.data.status;
      setPaymentData(result.data);

      if (paymentStatus === 'succeeded') {
        setStatus('success');
      } else if (paymentStatus === 'awaiting_payment_method') {
        setStatus('failed');
        setError(result.data.last_payment_error?.detail || 'Payment was declined');
      } else if (paymentStatus === 'processing') {
        // Still processing, check again after a delay
        setTimeout(() => checkPaymentStatus(), 2000);
      } else {
        setStatus('failed');
        setError(`Payment status: ${paymentStatus}`);
      }

    } catch (err) {
      console.error('Error checking payment status:', err);
      setStatus('error');
      setError(err instanceof Error ? err.message : 'Failed to verify payment');
    }
  };

  const handleReturnToOrder = () => {
    if (orderId) {
      router.push(`/orders/${orderId}`);
    } else {
      router.push('/orders');
    }
  };

  const handleReturnHome = () => {
    router.push('/');
  };

  const handleRetryPayment = () => {
    if (orderId) {
      router.push(`/checkout?order_id=${orderId}`);
    } else {
      router.push('/');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center gap-2">
            {status === 'loading' && <Loader2 className="h-6 w-6 animate-spin" />}
            {status === 'success' && <CheckCircle className="h-6 w-6 text-green-500" />}
            {(status === 'failed' || status === 'error') && <XCircle className="h-6 w-6 text-red-500" />}
            
            {status === 'loading' && 'Verifying Payment...'}
            {status === 'success' && 'Payment Successful!'}
            {status === 'failed' && 'Payment Failed'}
            {status === 'error' && 'Payment Error'}
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-4">
          {status === 'loading' && (
            <div className="text-center">
              <p className="text-gray-600">
                Please wait while we verify your payment...
              </p>
            </div>
          )}

          {status === 'success' && paymentData && (
            <div className="space-y-4">
              <Alert className="border-green-200 bg-green-50">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <AlertDescription className="text-green-700">
                  Your payment has been processed successfully!
                </AlertDescription>
              </Alert>
              
              <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Amount:</span>
                  <span className="font-medium">₱{(paymentData.amount / 100).toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Currency:</span>
                  <span className="font-medium">{paymentData.currency}</span>
                </div>
                {orderId && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Order ID:</span>
                    <span className="font-medium">{orderId}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-gray-600">Payment ID:</span>
                  <span className="font-medium text-xs">{paymentData.id}</span>
                </div>
              </div>

              <div className="space-y-2">
                <Button 
                  onClick={handleReturnToOrder} 
                  className="w-full bg-[#f3a823] hover:bg-[#ef7b06]"
                >
                  View Order Details
                </Button>
                <Button 
                  onClick={handleReturnHome} 
                  variant="outline" 
                  className="w-full"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Return to Home
                </Button>
              </div>
            </div>
          )}

          {(status === 'failed' || status === 'error') && (
            <div className="space-y-4">
              <Alert variant="destructive">
                <XCircle className="h-4 w-4" />
                <AlertDescription>
                  {error || 'An error occurred while processing your payment.'}
                </AlertDescription>
              </Alert>

              {paymentData && (
                <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Amount:</span>
                    <span className="font-medium">₱{(paymentData.amount / 100).toFixed(2)}</span>
                  </div>
                  {orderId && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Order ID:</span>
                      <span className="font-medium">{orderId}</span>
                    </div>
                  )}
                </div>
              )}

              <div className="space-y-2">
                <Button 
                  onClick={handleRetryPayment} 
                  className="w-full bg-[#f3a823] hover:bg-[#ef7b06]"
                >
                  Try Again
                </Button>
                <Button 
                  onClick={handleReturnHome} 
                  variant="outline" 
                  className="w-full"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Return to Home
                </Button>
              </div>
            </div>
          )}

          <div className="text-center">
            <p className="text-xs text-gray-500">
              If you have any questions about your payment, please contact our support team.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

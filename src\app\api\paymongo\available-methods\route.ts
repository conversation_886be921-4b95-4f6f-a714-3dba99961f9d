/**
 * Get Available Payment Methods from PayMongo API
 * GET /api/paymongo/available-methods
 */

import { NextRequest, NextResponse } from 'next/server';
import { paymongoSecretClient } from '@/lib/paymongo';

export async function GET(request: NextRequest) {
  try {
    console.log('Fetching available payment methods from PayMongo...');
    
    // Try to create a payment intent to see what methods are actually available
    const testPaymentIntent = {
      data: {
        attributes: {
          amount: 10000, // ₱100.00 in centavos
          currency: 'PHP',
          description: 'Test to check available payment methods',
          // Don't specify payment_method_allowed to see all available methods
          metadata: {
            test: 'available_methods',
            platform: 'tap2go',
          },
        },
      },
    };

    const response = await paymongoSecretClient.post('/payment_intents', testPaymentIntent);
    const paymentIntent = response.data.data;
    
    console.log('PayMongo response:', JSON.stringify(paymentIntent.attributes, null, 2));
    
    // Also try to fetch payment methods directly
    let availablePaymentMethods = [];
    try {
      const methodsResponse = await paymongoSecretClient.get('/payment_methods');
      console.log('Payment methods response:', JSON.stringify(methodsResponse.data, null, 2));
      availablePaymentMethods = methodsResponse.data.data || [];
    } catch (methodsError) {
      console.log('Could not fetch payment methods directly:', methodsError.response?.data || methodsError.message);
    }

    // Try to get account information
    let accountInfo = null;
    try {
      const accountResponse = await paymongoSecretClient.get('/account');
      accountInfo = accountResponse.data.data;
      console.log('Account info:', JSON.stringify(accountInfo, null, 2));
    } catch (accountError) {
      console.log('Could not fetch account info:', accountError.response?.data || accountError.message);
    }

    // Return comprehensive information
    return NextResponse.json({
      success: true,
      message: 'PayMongo available methods check completed',
      data: {
        environment: 'live',
        payment_intent: {
          id: paymentIntent.id,
          status: paymentIntent.attributes.status,
          payment_method_allowed: paymentIntent.attributes.payment_method_allowed,
          payment_method_options: paymentIntent.attributes.payment_method_options,
          amount: paymentIntent.attributes.amount,
          currency: paymentIntent.attributes.currency,
        },
        available_payment_methods: availablePaymentMethods,
        account_info: accountInfo,
        raw_payment_intent: paymentIntent.attributes,
      },
    });

  } catch (error) {
    console.error('PayMongo available methods check failed:', error);
    
    // Log detailed error information
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response headers:', error.response.headers);
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to check available payment methods',
        details: error instanceof Error ? error.message : 'Unknown error',
        environment: 'live',
        response_status: error.response?.status,
        response_data: error.response?.data || null,
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

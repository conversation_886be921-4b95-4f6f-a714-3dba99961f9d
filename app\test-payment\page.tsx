'use client';

/**
 * PayMongo Integration Test Page
 * Use this page to test PayMongo payment integration
 */

import React, { useState } from 'react';
import PaymentForm from '@/components/payment/PaymentForm';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, CreditCard, Info } from 'lucide-react';

export default function TestPaymentPage() {
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [paymentResult, setPaymentResult] = useState<any>(null);
  const [paymentError, setPaymentError] = useState<string>('');
  
  // Test order details
  const [testAmount, setTestAmount] = useState<number>(100);
  const [testDescription, setTestDescription] = useState<string>('Test Order - Jollibee Chickenjoy');
  const [testOrderId] = useState<string>(`test-order-${Date.now()}`);
  const [testCustomerId] = useState<string>(`test-customer-${Date.now()}`);

  const handlePaymentSuccess = (paymentData: any) => {
    setPaymentResult(paymentData);
    setPaymentError('');
    setShowPaymentForm(false);
  };

  const handlePaymentError = (error: string) => {
    setPaymentError(error);
    setPaymentResult(null);
  };

  const resetTest = () => {
    setShowPaymentForm(false);
    setPaymentResult(null);
    setPaymentError('');
  };

  const startPaymentTest = () => {
    if (testAmount < 20) {
      setPaymentError('Amount must be at least ₱20.00');
      return;
    }
    if (testAmount > 999999.99) {
      setPaymentError('Amount must not exceed ₱999,999.99');
      return;
    }
    
    setPaymentError('');
    setPaymentResult(null);
    setShowPaymentForm(true);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-6 w-6" />
              PayMongo Integration Test
            </CardTitle>
            <p className="text-gray-600">
              Test the PayMongo payment gateway integration for Tap2Go
            </p>
          </CardHeader>
        </Card>

        {/* Test Card Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="h-5 w-5" />
              Test Card Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-3">Successful Test Cards</h4>
                <div className="space-y-2 text-sm">
                  <div className="bg-green-50 p-3 rounded">
                    <strong>Visa:</strong> 4343 4343 4343 4345<br />
                    <strong>Mastercard:</strong> 5555 4444 4444 4457<br />
                    <strong>Expiry:</strong> Any future date<br />
                    <strong>CVC:</strong> Any 3 digits
                  </div>
                </div>
              </div>
              
              <div>
                <h4 className="font-medium mb-3">3D Secure Test Cards</h4>
                <div className="space-y-2 text-sm">
                  <div className="bg-blue-50 p-3 rounded">
                    <strong>3DS Required:</strong> 4120 0000 0000 0007<br />
                    <strong>3DS Supported:</strong> 5123 0000 0000 0001<br />
                    <strong>Expiry:</strong> Any future date<br />
                    <strong>CVC:</strong> Any 3 digits
                  </div>
                </div>
              </div>
            </div>
            
            <div className="mt-4 p-3 bg-yellow-50 rounded">
              <p className="text-sm text-yellow-800">
                <strong>Note:</strong> E-wallets (GCash, GrabPay, Maya) will redirect to test checkout pages where you can simulate successful or failed payments.
              </p>
            </div>
          </CardContent>
        </Card>

        <div className="grid md:grid-cols-2 gap-6">
          {/* Test Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Test Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="amount">Amount (₱)</Label>
                <Input
                  id="amount"
                  type="number"
                  value={testAmount}
                  onChange={(e) => setTestAmount(parseFloat(e.target.value) || 0)}
                  min="20"
                  max="999999.99"
                  step="0.01"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Minimum: ₱20.00, Maximum: ₱999,999.99
                </p>
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  value={testDescription}
                  onChange={(e) => setTestDescription(e.target.value)}
                  placeholder="Test order description"
                />
              </div>

              <div className="space-y-2">
                <div className="text-sm">
                  <strong>Order ID:</strong> {testOrderId}
                </div>
                <div className="text-sm">
                  <strong>Customer ID:</strong> {testCustomerId}
                </div>
              </div>

              <Button 
                onClick={startPaymentTest}
                disabled={showPaymentForm}
                className="w-full bg-[#f3a823] hover:bg-[#ef7b06]"
              >
                Start Payment Test
              </Button>

              {(paymentResult || paymentError) && (
                <Button 
                  onClick={resetTest}
                  variant="outline"
                  className="w-full"
                >
                  Reset Test
                </Button>
              )}
            </CardContent>
          </Card>

          {/* Payment Results */}
          <Card>
            <CardHeader>
              <CardTitle>Test Results</CardTitle>
            </CardHeader>
            <CardContent>
              {!showPaymentForm && !paymentResult && !paymentError && (
                <div className="text-center text-gray-500 py-8">
                  Configure your test and click "Start Payment Test" to begin
                </div>
              )}

              {paymentResult && (
                <Alert className="border-green-200 bg-green-50">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <AlertDescription>
                    <div className="space-y-2">
                      <div className="font-medium text-green-700">Payment Successful!</div>
                      <div className="text-sm space-y-1">
                        <div><strong>Status:</strong> {paymentResult.status}</div>
                        <div><strong>Payment ID:</strong> {paymentResult.id}</div>
                        {paymentResult.payments && paymentResult.payments.length > 0 && (
                          <div><strong>Payment Method:</strong> {paymentResult.payments[0].attributes.source?.type || 'Unknown'}</div>
                        )}
                      </div>
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              {paymentError && (
                <Alert variant="destructive">
                  <XCircle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-2">
                      <div className="font-medium">Payment Failed</div>
                      <div className="text-sm">{paymentError}</div>
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Payment Form */}
        {showPaymentForm && (
          <div className="flex justify-center">
            <PaymentForm
              orderId={testOrderId}
              amount={testAmount}
              description={testDescription}
              customerId={testCustomerId}
              onSuccess={handlePaymentSuccess}
              onError={handlePaymentError}
            />
          </div>
        )}

        {/* Environment Information */}
        <Card>
          <CardHeader>
            <CardTitle>Environment Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div>
                <strong>PayMongo Environment:</strong> {process.env.PAYMONGO_ENVIRONMENT || 'test'}
              </div>
              <div>
                <strong>API Base URL:</strong> https://api.paymongo.com/v1
              </div>
              <div>
                <strong>Webhook URL:</strong> {process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/webhooks/paymongo
              </div>
              <div>
                <strong>Return URL:</strong> {process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/payment/callback
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

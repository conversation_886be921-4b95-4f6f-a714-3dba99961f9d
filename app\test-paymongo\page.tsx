'use client';

/**
 * PayMongo Integration Test & Demo Page
 * Tests connection and displays available payment methods
 */

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CheckCircle, 
  XCircle, 
  Loader2, 
  CreditCard, 
  ExternalLink,
  Info,
  Play
} from 'lucide-react';

interface ConnectionTestResult {
  success: boolean;
  message?: string;
  data?: any;
  error?: string;
  details?: string;
}

export default function TestPayMongoPage() {
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'testing' | 'success' | 'error'>('idle');
  const [testResult, setTestResult] = useState<ConnectionTestResult | null>(null);

  const testConnection = async () => {
    setConnectionStatus('testing');
    setTestResult(null);

    try {
      const response = await fetch('/api/paymongo/test-connection');
      const result = await response.json();

      if (response.ok && result.success) {
        setConnectionStatus('success');
        setTestResult(result);
      } else {
        setConnectionStatus('error');
        setTestResult(result);
      }
    } catch (error) {
      setConnectionStatus('error');
      setTestResult({
        success: false,
        error: 'Network error',
        details: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  };

  // Auto-test connection on page load
  useEffect(() => {
    const timer = setTimeout(() => {
      testConnection();
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <Card>
          <CardHeader>
            <CardTitle className="text-center text-2xl font-bold text-[#f3a823]">
              PayMongo Integration Test
            </CardTitle>
            <p className="text-center text-gray-600">
              Testing PayMongo Payment Gateway for Tap2Go - Live Environment
            </p>
          </CardHeader>
        </Card>

        {/* Connection Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Connection Test
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span>PayMongo API Connection</span>
              <div className="flex items-center gap-2">
                {connectionStatus === 'testing' && (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                    <span className="text-blue-600">Testing...</span>
                  </>
                )}
                {connectionStatus === 'success' && (
                  <>
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-green-600">Connected</span>
                  </>
                )}
                {connectionStatus === 'error' && (
                  <>
                    <XCircle className="h-4 w-4 text-red-500" />
                    <span className="text-red-600">Failed</span>
                  </>
                )}
              </div>
            </div>

            {testResult && (
              <div className="space-y-3">
                {testResult.success ? (
                  <Alert className="border-green-200 bg-green-50">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <AlertDescription className="text-green-700">
                      <div className="space-y-2">
                        <div className="font-medium">{testResult.message}</div>
                        {testResult.data && (
                          <div className="text-sm space-y-1">
                            <div><strong>Environment:</strong> {testResult.data.environment}</div>
                            <div><strong>Payment Intent ID:</strong> {testResult.data.payment_intent_id}</div>
                            <div><strong>Status:</strong> {testResult.data.status}</div>
                            <div><strong>Amount:</strong> ₱{(testResult.data.amount / 100).toFixed(2)}</div>
                            <div><strong>Currency:</strong> {testResult.data.currency}</div>
                            <div><strong>Payment Methods:</strong> {testResult.data.payment_methods_allowed?.join(', ')}</div>
                          </div>
                        )}
                      </div>
                    </AlertDescription>
                  </Alert>
                ) : (
                  <Alert variant="destructive">
                    <XCircle className="h-4 w-4" />
                    <AlertDescription>
                      <div className="space-y-2">
                        <div className="font-medium">{testResult.error}</div>
                        {testResult.details && (
                          <div className="text-sm">{testResult.details}</div>
                        )}
                        {testResult.response_data && (
                          <div className="text-xs bg-red-100 p-2 rounded mt-2">
                            <strong>API Response:</strong>
                            <pre className="whitespace-pre-wrap">
                              {JSON.stringify(testResult.response_data, null, 2)}
                            </pre>
                          </div>
                        )}
                      </div>
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            )}

            <Button 
              onClick={testConnection}
              disabled={connectionStatus === 'testing'}
              variant="outline"
              className="w-full"
            >
              {connectionStatus === 'testing' ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Testing Connection...
                </>
              ) : (
                'Test Connection Again'
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Demo Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Play className="h-5 w-5" />
              Demo & Testing
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-2 gap-4">
              <Link href="/sample-checkout">
                <Button className="w-full h-20 flex flex-col items-center justify-center gap-2 bg-[#f3a823] hover:bg-[#ef7b06]">
                  <CreditCard className="h-6 w-6" />
                  <span>Sample Checkout Demo</span>
                  <span className="text-xs opacity-90">View all payment methods</span>
                </Button>
              </Link>

              <Link href="/test-payment">
                <Button variant="outline" className="w-full h-20 flex flex-col items-center justify-center gap-2">
                  <ExternalLink className="h-6 w-6" />
                  <span>Full Payment Test</span>
                  <span className="text-xs opacity-70">Test actual payments</span>
                </Button>
              </Link>
            </div>

            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                <strong>Sample Checkout Demo:</strong> Shows all available payment methods without processing actual payments.
                Perfect for seeing what payment options will be displayed to customers.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>

        {/* Environment Information */}
        <Card>
          <CardHeader>
            <CardTitle>Environment Configuration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <div><strong>Environment:</strong> Live Production</div>
                <div><strong>Business Status:</strong> Verified ✅</div>
                <div><strong>API Base URL:</strong> https://api.paymongo.com/v1</div>
              </div>
              <div className="space-y-2">
                <div><strong>Public Key:</strong> pk_live_UJhf...9Qb7</div>
                <div><strong>Webhook URL:</strong> /api/webhooks/paymongo</div>
                <div><strong>Return URL:</strong> /payment/callback</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Available Payment Methods */}
        {connectionStatus === 'success' && testResult?.data?.payment_methods_allowed && (
          <Card>
            <CardHeader>
              <CardTitle>Available Payment Methods</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {testResult.data.payment_methods_allowed.map((method: string) => (
                  <div key={method} className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm font-medium capitalize">
                      {method.replace('_', ' ')}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Next Steps</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div className="flex items-start gap-2">
                <span className="bg-[#f3a823] text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">1</span>
                <span>Click "Sample Checkout Demo" to see all available payment methods</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="bg-[#f3a823] text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">2</span>
                <span>Test the payment flow without processing actual payments</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="bg-[#f3a823] text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">3</span>
                <span>When ready, use "Full Payment Test" for actual payment testing</span>
              </div>
              <div className="flex items-start gap-2">
                <span className="bg-[#f3a823] text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold">4</span>
                <span>Set up webhooks for production deployment</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

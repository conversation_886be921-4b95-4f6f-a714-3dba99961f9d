'use client';

/**
 * Simple PayMongo Payment Methods Demo
 * Shows all available payment methods in a clean interface
 */

import React, { useState } from 'react';

export default function PaymentDemoPage() {
  const [selectedMethod, setSelectedMethod] = useState<string>('');

  // Sample order details
  const orderDetails = {
    orderId: 'TAP2GO-' + Date.now(),
    restaurant: 'Jollibee - SM Mall of Asia',
    items: [
      { name: 'Chickenjoy (2pcs)', price: 180, quantity: 1 },
      { name: 'Jolly Spaghetti', price: 85, quantity: 1 },
      { name: 'Peach Mango Pie', price: 45, quantity: 2 },
    ],
    subtotal: 355,
    deliveryFee: 49,
    serviceFee: 15,
    total: 419,
  };

  // All PayMongo payment methods
  const paymentMethods = [
    {
      id: 'card',
      name: 'Credit/Debit Card',
      description: 'Visa, Mastercard, and other major cards',
      icon: '💳',
      fees: '3.5% + ₱15',
      color: 'bg-blue-50 border-blue-200',
    },
    {
      id: 'gcash',
      name: 'G<PERSON>ash',
      description: 'Pay using your GCash wallet',
      icon: '📱',
      fees: '2.5%',
      color: 'bg-blue-50 border-blue-200',
    },
    {
      id: 'grab_pay',
      name: 'GrabPay',
      description: 'Pay using your GrabPay wallet',
      icon: '🚗',
      fees: '2.5%',
      color: 'bg-green-50 border-green-200',
    },
    {
      id: 'paymaya',
      name: 'Maya',
      description: 'Pay using your Maya wallet',
      icon: '💰',
      fees: '2.5%',
      color: 'bg-green-50 border-green-200',
    },
    {
      id: 'billease',
      name: 'BillEase',
      description: 'Buy now, pay later in installments',
      icon: '🏦',
      fees: 'Varies',
      color: 'bg-purple-50 border-purple-200',
    },
    {
      id: 'dob',
      name: 'Online Banking',
      description: 'BPI, UBP, BDO, Landbank, Metrobank',
      icon: '🏛️',
      fees: 'Varies by bank',
      color: 'bg-indigo-50 border-indigo-200',
    },
    {
      id: 'qrph',
      name: 'QR Ph',
      description: 'Scan to pay with any QR Ph app',
      icon: '📱',
      fees: '1.5%',
      color: 'bg-red-50 border-red-200',
    },
  ];

  const handleMethodSelect = (methodId: string) => {
    setSelectedMethod(methodId);
  };

  const handleProceedToPayment = () => {
    const method = paymentMethods.find(m => m.id === selectedMethod);
    if (method) {
      alert(`Selected payment method: ${method.name}\n\nThis is a demo. No actual payment will be processed.`);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-md p-6 text-center">
          <h1 className="text-2xl font-bold text-[#f3a823] mb-2">
            Tap2Go Payment Methods Demo
          </h1>
          <p className="text-gray-600">
            PayMongo Payment Gateway - Live Environment (Business Verified)
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-6">
          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-semibold mb-4 flex items-center gap-2">
                ✅ Order Summary
              </h2>
              
              <div className="space-y-4">
                <div>
                  <p className="font-medium">{orderDetails.restaurant}</p>
                  <p className="text-sm text-gray-600">Order ID: {orderDetails.orderId}</p>
                </div>

                <div className="space-y-2">
                  {orderDetails.items.map((item, index) => (
                    <div key={index} className="flex justify-between text-sm">
                      <span>{item.quantity}x {item.name}</span>
                      <span>₱{(item.price * item.quantity).toFixed(2)}</span>
                    </div>
                  ))}
                </div>

                <div className="border-t pt-2 space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>Subtotal</span>
                    <span>₱{orderDetails.subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Delivery Fee</span>
                    <span>₱{orderDetails.deliveryFee.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Service Fee</span>
                    <span>₱{orderDetails.serviceFee.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between font-bold text-lg border-t pt-2">
                    <span>Total</span>
                    <span className="text-[#f3a823]">₱{orderDetails.total.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Payment Methods */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h2 className="text-lg font-semibold mb-4">Choose Payment Method</h2>
              <p className="text-sm text-gray-600 mb-6">
                Select your preferred payment method to complete your order
              </p>

              <div className="space-y-3 mb-6">
                {paymentMethods.map((method) => (
                  <div
                    key={method.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${
                      selectedMethod === method.id
                        ? 'border-[#f3a823] bg-orange-50'
                        : 'border-gray-200 hover:border-gray-300'
                    } ${method.color}`}
                    onClick={() => handleMethodSelect(method.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="text-2xl">{method.icon}</div>
                        <div>
                          <div className="flex items-center gap-2">
                            <h3 className="font-medium">{method.name}</h3>
                            <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                              {method.id.toUpperCase()}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600">{method.description}</p>
                          <p className="text-xs text-gray-500">Processing fee: {method.fees}</p>
                        </div>
                      </div>
                      <div className="flex-shrink-0">
                        {selectedMethod === method.id && (
                          <div className="text-[#f3a823] text-xl">✅</div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="space-y-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start gap-2">
                    <span className="text-blue-600">ℹ️</span>
                    <div className="text-sm text-blue-800">
                      <strong>Demo Mode:</strong> This is a demonstration of available payment methods. 
                      No actual payment will be processed when you click "Proceed to Payment".
                    </div>
                  </div>
                </div>

                <button
                  onClick={handleProceedToPayment}
                  disabled={!selectedMethod}
                  className={`w-full py-3 px-4 rounded-lg text-white font-medium transition-colors ${
                    selectedMethod 
                      ? 'bg-[#f3a823] hover:bg-[#ef7b06]' 
                      : 'bg-gray-300 cursor-not-allowed'
                  }`}
                >
                  {selectedMethod 
                    ? `Proceed to Payment - ₱${orderDetails.total.toFixed(2)}`
                    : 'Select a payment method'
                  }
                </button>

                <div className="text-center">
                  <p className="text-xs text-gray-500">
                    🔒 Secured by PayMongo • Live Environment • Business Verified
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Environment Info */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-sm font-semibold mb-3">Environment Information</h3>
          <div className="grid md:grid-cols-3 gap-4 text-sm">
            <div><strong>Environment:</strong> Live Production</div>
            <div><strong>Business Status:</strong> Verified ✅</div>
            <div><strong>API Version:</strong> v1</div>
          </div>
        </div>
      </div>
    </div>
  );
}

'use client';

/**
 * Sample Checkout Page - PayMongo Payment Methods Display
 * Shows all available payment methods without processing actual payments
 */

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CreditCard, 
  Smartphone, 
  QrCode, 
  Building2, 
  Wallet,
  CheckCircle,
  Info,
  Loader2
} from 'lucide-react';

interface PaymentMethod {
  id: string;
  name: string;
  type: string;
  description: string;
  icon: React.ReactNode;
  available: boolean;
  fees?: string;
}

export default function SampleCheckoutPage() {
  const [selectedMethod, setSelectedMethod] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);

  // Sample order details
  const orderDetails = {
    orderId: 'TAP2GO-' + Date.now(),
    items: [
      { name: 'Jollibee Chickenjoy (2pcs)', price: 180, quantity: 1 },
      { name: 'Jolly Spaghetti', price: 85, quantity: 1 },
      { name: 'Peach Mango Pie', price: 45, quantity: 2 },
    ],
    subtotal: 355,
    deliveryFee: 49,
    serviceFee: 15,
    total: 419,
    restaurant: 'Jollibee - SM Mall of Asia',
    deliveryAddress: '123 Seaside Blvd, Pasay City',
    estimatedDelivery: '25-35 minutes',
  };

  // Define all PayMongo payment methods
  const allPaymentMethods: PaymentMethod[] = [
    {
      id: 'card',
      name: 'Credit/Debit Card',
      type: 'card',
      description: 'Visa, Mastercard, and other major cards',
      icon: <CreditCard className="h-6 w-6" />,
      available: true,
      fees: '3.5% + ₱15',
    },
    {
      id: 'gcash',
      name: 'GCash',
      type: 'ewallet',
      description: 'Pay using your GCash wallet',
      icon: <Wallet className="h-6 w-6 text-blue-600" />,
      available: true,
      fees: '2.5%',
    },
    {
      id: 'grab_pay',
      name: 'GrabPay',
      type: 'ewallet',
      description: 'Pay using your GrabPay wallet',
      icon: <Smartphone className="h-6 w-6 text-green-600" />,
      available: true,
      fees: '2.5%',
    },
    {
      id: 'paymaya',
      name: 'Maya',
      type: 'ewallet',
      description: 'Pay using your Maya wallet',
      icon: <Wallet className="h-6 w-6 text-green-500" />,
      available: true,
      fees: '2.5%',
    },
    {
      id: 'billease',
      name: 'BillEase',
      type: 'bnpl',
      description: 'Buy now, pay later in installments',
      icon: <Building2 className="h-6 w-6 text-purple-600" />,
      available: true,
      fees: 'Varies',
    },
    {
      id: 'dob',
      name: 'Online Banking',
      type: 'banking',
      description: 'BPI, UBP, BDO, Landbank, Metrobank',
      icon: <Building2 className="h-6 w-6 text-blue-800" />,
      available: true,
      fees: 'Varies by bank',
    },
    {
      id: 'qrph',
      name: 'QR Ph',
      type: 'qr',
      description: 'Scan to pay with any QR Ph app',
      icon: <QrCode className="h-6 w-6 text-red-600" />,
      available: true,
      fees: '1.5%',
    },
  ];

  useEffect(() => {
    // Simulate loading and set available payment methods
    const timer = setTimeout(() => {
      setPaymentMethods(allPaymentMethods);
      setLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  const handleMethodSelect = (methodId: string) => {
    setSelectedMethod(methodId);
  };

  const handleProceedToPayment = () => {
    const method = paymentMethods.find(m => m.id === selectedMethod);
    if (method) {
      alert(`Selected payment method: ${method.name}\n\nThis is a demo. No actual payment will be processed.`);
    }
  };

  const getMethodTypeColor = (type: string) => {
    switch (type) {
      case 'card': return 'bg-blue-100 text-blue-800';
      case 'ewallet': return 'bg-green-100 text-green-800';
      case 'bnpl': return 'bg-purple-100 text-purple-800';
      case 'banking': return 'bg-indigo-100 text-indigo-800';
      case 'qr': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <Card>
          <CardHeader>
            <CardTitle className="text-center text-2xl font-bold text-[#f3a823]">
              Tap2Go Checkout Demo
            </CardTitle>
            <p className="text-center text-gray-600">
              PayMongo Payment Gateway Integration - Live Environment
            </p>
          </CardHeader>
        </Card>

        <div className="grid lg:grid-cols-3 gap-6">
          {/* Order Summary */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  Order Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="font-medium">{orderDetails.restaurant}</p>
                  <p className="text-sm text-gray-600">{orderDetails.deliveryAddress}</p>
                  <p className="text-sm text-gray-600">Est. delivery: {orderDetails.estimatedDelivery}</p>
                </div>

                <div className="space-y-2">
                  {orderDetails.items.map((item, index) => (
                    <div key={index} className="flex justify-between text-sm">
                      <span>{item.quantity}x {item.name}</span>
                      <span>₱{(item.price * item.quantity).toFixed(2)}</span>
                    </div>
                  ))}
                </div>

                <div className="border-t pt-2 space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>Subtotal</span>
                    <span>₱{orderDetails.subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Delivery Fee</span>
                    <span>₱{orderDetails.deliveryFee.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Service Fee</span>
                    <span>₱{orderDetails.serviceFee.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between font-bold text-lg border-t pt-2">
                    <span>Total</span>
                    <span className="text-[#f3a823]">₱{orderDetails.total.toFixed(2)}</span>
                  </div>
                </div>

                <div className="text-xs text-gray-500">
                  Order ID: {orderDetails.orderId}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Payment Methods */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Choose Payment Method</CardTitle>
                <p className="text-sm text-gray-600">
                  Select your preferred payment method to complete your order
                </p>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <Loader2 className="h-8 w-8 animate-spin text-[#f3a823]" />
                    <span className="ml-2">Loading payment methods...</span>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {paymentMethods.map((method) => (
                      <div
                        key={method.id}
                        className={`border rounded-lg p-4 cursor-pointer transition-all ${
                          selectedMethod === method.id
                            ? 'border-[#f3a823] bg-orange-50'
                            : 'border-gray-200 hover:border-gray-300'
                        } ${!method.available ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={() => method.available && handleMethodSelect(method.id)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className="flex-shrink-0">
                              {method.icon}
                            </div>
                            <div>
                              <div className="flex items-center gap-2">
                                <h3 className="font-medium">{method.name}</h3>
                                <Badge 
                                  variant="secondary" 
                                  className={getMethodTypeColor(method.type)}
                                >
                                  {method.type.toUpperCase()}
                                </Badge>
                                {!method.available && (
                                  <Badge variant="destructive">Unavailable</Badge>
                                )}
                              </div>
                              <p className="text-sm text-gray-600">{method.description}</p>
                              {method.fees && (
                                <p className="text-xs text-gray-500">Processing fee: {method.fees}</p>
                              )}
                            </div>
                          </div>
                          <div className="flex-shrink-0">
                            {selectedMethod === method.id && (
                              <CheckCircle className="h-5 w-5 text-[#f3a823]" />
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {!loading && (
                  <div className="mt-6 space-y-4">
                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertDescription>
                        <strong>Demo Mode:</strong> This is a demonstration of available payment methods. 
                        No actual payment will be processed when you click "Proceed to Payment".
                      </AlertDescription>
                    </Alert>

                    <Button
                      onClick={handleProceedToPayment}
                      disabled={!selectedMethod}
                      className="w-full bg-[#f3a823] hover:bg-[#ef7b06] text-white py-3 text-lg"
                    >
                      {selectedMethod 
                        ? `Proceed to Payment - ₱${orderDetails.total.toFixed(2)}`
                        : 'Select a payment method'
                      }
                    </Button>

                    <div className="text-center">
                      <p className="text-xs text-gray-500">
                        🔒 Secured by PayMongo • Live Environment • Business Verified
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Environment Info */}
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Environment Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-3 gap-4 text-sm">
              <div>
                <strong>Environment:</strong> Live Production
              </div>
              <div>
                <strong>Business Status:</strong> Verified ✅
              </div>
              <div>
                <strong>API Version:</strong> v1
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

/**
 * Attach Payment Method API Route
 * POST /api/payments/attach-method
 */

import { NextRequest, NextResponse } from 'next/server';
import { PayMongoService } from '@/server/services/paymongoService';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate required fields
    const { payment_intent_id, payment_method_id, client_key, return_url } = body;
    
    if (!payment_intent_id || typeof payment_intent_id !== 'string') {
      return NextResponse.json(
        { error: 'Payment Intent ID is required' },
        { status: 400 }
      );
    }

    if (!payment_method_id || typeof payment_method_id !== 'string') {
      return NextResponse.json(
        { error: 'Payment Method ID is required' },
        { status: 400 }
      );
    }

    if (!client_key || typeof client_key !== 'string') {
      return NextResponse.json(
        { error: 'Client key is required' },
        { status: 400 }
      );
    }

    // Attach payment method to payment intent
    const result = await PayMongoService.attachPaymentMethod({
      payment_intent_id,
      payment_method_id,
      client_key,
      return_url: return_url || `${process.env.NEXT_PUBLIC_APP_URL}/payment/callback`,
    });

    // Return success response
    return NextResponse.json({
      success: true,
      data: {
        id: result.data.id,
        status: result.data.attributes.status,
        next_action: result.data.attributes.next_action,
        last_payment_error: result.data.attributes.last_payment_error,
        payments: result.data.attributes.payments,
      },
    });

  } catch (error) {
    console.error('Attach Payment Method API Error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to attach payment method',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

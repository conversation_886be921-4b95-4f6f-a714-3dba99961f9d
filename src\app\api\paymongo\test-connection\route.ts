/**
 * Test PayMongo Connection API Route
 * GET /api/paymongo/test-connection
 */

import { NextRequest, NextResponse } from 'next/server';
import { paymongoSecretClient } from '@/lib/paymongo';

export async function GET(request: NextRequest) {
  try {
    console.log('Testing PayMongo connection...');
    
    // Test the connection by trying to create a simple payment intent
    const testPaymentIntent = {
      data: {
        attributes: {
          amount: 10000, // ₱100.00 in centavos
          currency: 'PHP',
          description: 'Test connection to PayMongo API',
          payment_method_allowed: ['card', 'gcash', 'grab_pay', 'paymaya'],
          metadata: {
            test: 'connection',
            platform: 'tap2go',
          },
        },
      },
    };

    const response = await paymongoSecretClient.post('/payment_intents', testPaymentIntent);
    
    console.log('PayMongo connection successful!');
    console.log('Payment Intent ID:', response.data.data.id);
    
    // Return success response with connection details
    return NextResponse.json({
      success: true,
      message: 'PayMongo connection successful',
      data: {
        environment: process.env.PAYMONGO_ENVIRONMENT,
        payment_intent_id: response.data.data.id,
        client_key: response.data.data.attributes.client_key,
        status: response.data.data.attributes.status,
        payment_methods_allowed: response.data.data.attributes.payment_method_allowed,
        amount: response.data.data.attributes.amount,
        currency: response.data.data.attributes.currency,
      },
    });

  } catch (error) {
    console.error('PayMongo connection test failed:', error);
    
    // Log more details about the error
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: 'PayMongo connection failed',
        details: error instanceof Error ? error.message : 'Unknown error',
        environment: process.env.PAYMONGO_ENVIRONMENT,
        response_data: error.response?.data || null,
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
